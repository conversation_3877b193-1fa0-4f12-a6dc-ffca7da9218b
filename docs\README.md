# MicroSLM Monitoring System

## Overview

The MicroSLM Monitoring System is an asynchronous Python service designed to monitor industrial manufacturing equipment through various communication protocols (primarily OPC UA and Modbus) and store the collected data in InfluxDB for real-time analytics and visualization.

## System Architecture

The system follows a modular architecture with clear separation of concerns:

![Class Diagram](class_diagram.png)

*Figure 1: Class diagram showing the relationships between core components*

### Core Components

1. **Main Application (`main.py`)** - Entry point and orchestrator
2. **Configuration Management (`config.py`)** - YAML-based configuration loading
3. **Logging System (`logger.py`)** - Centralized logging with file rotation
4. **Database Writer (`influx_writer.py`)** - InfluxDB data persistence
5. **Protocol Monitors (`monitors/`)** - Protocol-specific monitoring implementations

## System Flow

![Sequence Diagram](sequence_diagram.png)

*Figure 2: Sequence diagram showing the interaction flow between components*

### 1. Application Initialization

The system starts with a structured initialization process:

1. **Logging Setup**: Creates timestamped log files in the `logs/` directory with configurable log levels
2. **Configuration Loading**: Loads environment-specific settings from `config.yaml`
3. **Environment Detection**: Determines runtime environment (development/production)
4. **Database Connection**: Initializes InfluxDB writer with connection validation

### 2. Machine Monitoring Setup

For each configured machine:

1. **Protocol Detection**: Identifies the communication protocol (OPC UA/Modbus)
2. **Task Creation**: Creates asynchronous monitoring tasks
3. **Connection Management**: Establishes connections with retry logic
4. **Data Subscription**: Sets up real-time data change notifications

### 3. Data Collection and Storage

The monitoring process follows this pattern:

1. **Data Reception**: Monitors receive data changes from industrial equipment
2. **Data Processing**: Raw data is formatted with timestamps and metadata tags
3. **Database Writing**: Processed data points are written to InfluxDB asynchronously
4. **Error Handling**: Connection failures trigger automatic reconnection attempts

### 4. Continuous Operation

The system maintains:

- **Persistent Connections**: Long-running connections with automatic reconnection
- **Concurrent Monitoring**: Multiple machines monitored simultaneously
- **Graceful Shutdown**: Proper cleanup on application termination

## Component Interactions

### Configuration Flow
- `main.py` calls `config.py` to load YAML configuration
- Configuration is validated and passed to monitoring components
- Environment-specific settings are applied

### Logging Flow
- `logger.py` sets up centralized logging infrastructure
- All components use the same logging namespace for consistency
- Log rotation and formatting are handled automatically

### Data Flow
- Protocol monitors (`opcua_monitor.py`) receive data from equipment
- Data is formatted and passed to `influx_writer.py`
- InfluxDB writer handles asynchronous database operations
- Error conditions are logged through the central logging system

### Monitoring Flow
- `main.py` creates monitoring tasks for each configured machine
- Each monitor maintains its own connection and subscription
- Data changes trigger immediate database writes
- Connection failures are handled with exponential backoff retry logic

## Key Features

### Asynchronous Architecture
- Non-blocking I/O operations for high performance
- Concurrent monitoring of multiple machines
- Efficient resource utilization

### Robust Error Handling
- Automatic reconnection on connection failures
- Comprehensive logging of all operations
- Graceful degradation under error conditions

### Flexible Configuration
- YAML-based configuration for easy maintenance
- Environment-specific settings (development/production)
- Hot-swappable machine configurations

### Scalable Data Storage
- InfluxDB time-series database for efficient storage
- Asynchronous writes for high throughput
- Configurable batch sizes and flush intervals

## Configuration Structure

The system uses a hierarchical configuration structure:

```
Environment (development/production)
├── machines/
│   ├── machine_name/
│   │   ├── type (equipment type)
│   │   ├── protocol configuration (opcua/modbus)
│   │   ├── node mappings
│   │   └── monitoring settings
│   └── ...
└── influxdb/
    ├── connection settings
    └── performance tuning
```

## Monitoring Protocols

### OPC UA Monitoring
- Real-time subscription-based data collection
- Automatic node discovery and mapping
- Authentication support for secure connections
- Configurable subscription intervals

### Modbus Monitoring (Future)
- Polling-based data collection
- Support for multiple register types
- Configurable polling intervals
- Error detection and recovery

## Data Model

Data points stored in InfluxDB follow this structure:

- **Measurement**: Table name (e.g., "machine_sensors", "power_consumption")
- **Tags**: Metadata for data organization (machine_name, equipment_type)
- **Fields**: Actual measured values (temperature, pressure, status)
- **Timestamp**: Data collection time (from source or system time)

## Deployment Considerations

### Development Environment
- Local InfluxDB instance or development server
- Verbose logging for debugging
- Simulated or test equipment connections

### Production Environment
- Production InfluxDB cluster
- Optimized logging levels
- Real industrial equipment connections
- Enhanced security and authentication

## Monitoring and Observability

The system provides comprehensive observability through:

- **Structured Logging**: Detailed operation logs with timestamps
- **Connection Status**: Real-time connection health monitoring
- **Data Flow Metrics**: Success/failure rates for data operations
- **Error Tracking**: Detailed error logging with context

## Documentation Diagrams

This documentation includes two Mermaid diagrams that can be converted to PNG format:

1. **Class Diagram** (`class_diagram.md`) - Shows the relationships between all classes and components
2. **Sequence Diagram** (`sequence_diagram.md`) - Illustrates the interaction flow during system operation

### Converting Diagrams to PNG

To convert the Mermaid diagrams to PNG format for embedding:

1. **Using Mermaid CLI**:
   ```bash
   npm install -g @mermaid-js/mermaid-cli
   mmdc -i docs/class_diagram.md -o docs/class_diagram.png
   mmdc -i docs/sequence_diagram.md -o docs/sequence_diagram.png
   ```

2. **Using Online Tools**:
   - Copy the Mermaid code from the `.md` files
   - Paste into [Mermaid Live Editor](https://mermaid.live/)
   - Export as PNG

3. **Using VS Code Extension**:
   - Install the "Mermaid Markdown Syntax Highlighting" extension
   - Open the diagram files and use the export functionality

## Future Enhancements

- Support for additional protocols (Modbus TCP, Ethernet/IP)
- Web-based configuration interface
- Real-time dashboard for system health
- Advanced alerting and notification systems
- Data validation and quality checks
