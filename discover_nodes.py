# discover_nodes.py
import argparse
import asyncio
import logging
import yaml
import os
from datetime import datetime
from asyncua import Client, Node
from asyncua.ua import UaStatusCodeError
from urllib.parse import urlparse

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
log = logging.getLogger(__name__)

class OPCUANodeDiscoverer:
    """
    Discovers OPC UA nodes from a server and generates configuration files.
    """
    
    def __init__(self, server_url, timeout=10, max_depth=5):
        """
        Initialize the node discoverer.
        
        Args:
            server_url (str): OPC UA server URL
            timeout (int): Connection timeout in seconds
            max_depth (int): Maximum recursion depth for node discovery
        """
        self.server_url = server_url
        self.timeout = timeout
        self.max_depth = max_depth
        self.discovered_nodes = {}
        self.client = None
        
    async def connect_to_server(self):
        """
        Connect to the OPC UA server with proper error handling.
        
        Returns:
            bool: True if connection successful, False otherwise
        """
        try:
            log.info(f"Attempting to connect to OPC UA server: {self.server_url}")
            self.client = Client(url=self.server_url, timeout=self.timeout)
            await self.client.connect()
            log.info("Successfully connected to OPC UA server")
            return True
            
        except TimeoutError:
            log.error(f"Connection timed out after {self.timeout} seconds")
            return False
        except UaStatusCodeError as e:
            log.error(f"OPC UA status error: {e}")
            return False
        except ConnectionRefusedError:
            log.error(f"Connection refused. Make sure the server is running at {self.server_url}")
            return False
        except Exception as e:
            log.error(f"Unexpected error connecting to server: {e}")
            return False
    
    async def disconnect_from_server(self):
        """
        Safely disconnect from the OPC UA server.
        """
        try:
            if self.client:
                await self.client.disconnect()
                log.info("Disconnected from OPC UA server")
        except Exception as e:
            log.warning(f"Error during disconnection: {e}")
    
    async def recursive_browse(self, node, depth=0, path=""):
        """
        Recursively browse the OPC UA node tree and collect node information.
        Only collects nodes from custom namespaces (ns > 0) to avoid system nodes.
        
        Args:
            node: The OPC UA node to browse
            depth (int): Current recursion depth
            path (str): Path to the current node for context
        """
        # Prevent infinite recursion
        if depth > self.max_depth:
            log.warning(f"Maximum depth ({self.max_depth}) reached at path: {path}")
            return
        
        try:
            # Get node information
            browse_name = await node.read_browse_name()
            # Uses clean string representation like "ns=2;i=3"
            node_id = str(node)
            display_name = browse_name.Name
            
            # Filter out system nodes (namespace 0)
            namespace_index = node.nodeid.NamespaceIndex
            if namespace_index == 0:
                log.debug(f"Skipping system node (ns=0): {display_name}")
                # Still browse children in case there are custom nodes underneath
                try:
                    child_nodes = await node.get_children()
                    for child in child_nodes:
                        await self.recursive_browse(child, depth + 1, path)
                except Exception as e:
                    log.debug(f"Could not browse children of system node {display_name}: {e}")
                return
            
            # Create path for this node
            current_path = f"{path}/{display_name}" if path else display_name
            
            # Log progress with indentation to represent depth
            indent = "  " * depth
            log.debug(f"{indent}Browsing: {display_name} ({node_id}) [ns={namespace_index}]")
            
            # Try to read the node value to determine if it's a data node
            try:
                value = await node.read_value()
                data_type = await node.read_data_type()
                
                self.discovered_nodes[current_path] = {
                    'node_id': node_id,
                    'display_name': display_name,
                    'value': value,
                    'data_type': str(data_type),
                    'path': current_path,
                    'namespace': namespace_index
                }
                log.info(f"Found sensor node: {current_path} = {value} [ns={namespace_index}] -> {node_id}")
                
            except Exception:
                # This node might not have a value (could be a folder/object)
                log.debug(f"Node {display_name} has no readable value (likely a container)")
            
            try:
                child_nodes = await node.get_children()
                for child in child_nodes:
                    await self.recursive_browse(child, depth + 1, current_path)
                    
            except Exception as e:
                log.debug(f"Could not browse children of {display_name}: {e}")
                
        except Exception as e:
            log.warning(f"Error browsing node at depth {depth}: {e}")
    
    async def discover_nodes(self):
        """
        Main method to discover all nodes on the server.
        
        Returns:
            dict: Dictionary of discovered nodes
        """
        if not await self.connect_to_server():
            return {}
        
        try:
            log.info("Starting node discovery...")
            
            # Start from the Objects root node (standard OPC UA starting point is i=85)
            root_node = self.client.get_node("i=85")
            
            await self.recursive_browse(root_node)
            
            log.info(f"Discovery complete. Found {len(self.discovered_nodes)} data nodes.")
            return self.discovered_nodes
            
        except Exception as e:
            log.error(f"Error during node discovery: {e}")
            return {}
        finally:
            await self.disconnect_from_server()
    
    def generate_machine_config(self, machine_name=None, machine_type="unknown"):
        """
        Generate a machine configuration dictionary from discovered nodes.
        
        Args:
            machine_name (str): Name for the machine
            machine_type (str): Type of machine (e.g., '3d_printer', 'cnc_machine')
            
        Returns:
            dict: Machine configuration dictionary
        """
        if not machine_name:
            # Generate default machine name from URL
            parsed_url = urlparse(self.server_url)
            machine_name = f"Machine_{parsed_url.hostname}_{parsed_url.port}"
        
        # Detect the primary namespace from discovered nodes
        namespaces = set()
        for node_info in self.discovered_nodes.values():
            if 'namespace' in node_info:
                namespaces.add(node_info['namespace'])
        
        # Use the lowest non-zero namespace as the primary namespace
        primary_namespace = min(ns for ns in namespaces if ns > 0) if namespaces else 2
        
        # Select meaningful nodes and clean up field names
        selected_nodes = {}
        for path, node_info in self.discovered_nodes.items():
            # Use the last part of the path as the field name, cleaned up
            field_name = path.split('/')[-1].lower().replace(' ', '_').replace('-', '_')
            
            # Remove common prefixes/suffixes to make field names cleaner
            field_name = field_name.replace('machine_', '').replace('_value', '').replace('_current', '')
            
            # Store the clean node ID (like "ns=2;i=3" format)
            selected_nodes[field_name] = node_info['node_id']
        
        log.info(f"Generated config for machine '{machine_name}' with {len(selected_nodes)} nodes from namespace {primary_namespace}")
        
        machine_config = {
            machine_name: {
                "type": machine_type,
                "opcua": {
                    "url": self.server_url,
                    "namespace": primary_namespace,
                    "authentication": {
                        "enabled": False,
                        "username": None,
                        "password": None
                    }
                },
                "nodes": selected_nodes,
                "monitoring": {
                    "subscription_interval_ms": 1000,
                    "connection_timeout_seconds": 10,
                    "retry_attempts": 3,
                    "retry_delay_seconds": 5
                }
            }
        }
        
        return machine_config
    
    def save_config_to_file(self, config, filename=None):
        """
        Save the configuration to a YAML file.
        
        Args:
            config (dict): Configuration dictionary
            filename (str): Output filename (optional)
        """
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            parsed_url = urlparse(self.server_url)
            filename = f"discovered_config_{parsed_url.hostname}_{parsed_url.port}_{timestamp}.yaml"
        
        try:
            # Load existing config if it exists
            existing_config = {}
            if os.path.exists("config.yaml"):
                try:
                    with open("config.yaml", 'r') as f:
                        existing_config = yaml.safe_load(f) or {}
                    log.info("Loaded existing config.yaml")
                except Exception as e:
                    log.warning(f"Could not load existing config.yaml: {e}")
            
            # Merge configurations
            if 'development' not in existing_config:
                existing_config['development'] = {}
            if 'machines' not in existing_config['development']:
                existing_config['development']['machines'] = {}
            
            # Add discovered machines to development config
            existing_config['development']['machines'].update(config)
            
            # Save the updated configuration
            with open(filename, 'w') as f:
                yaml.dump(existing_config, f, default_flow_style=False, indent=2)
            
            log.info(f"Configuration saved to: {filename}")
            
        except Exception as e:
            log.error(f"Error saving configuration to file: {e}")

async def discover_and_configure(server_url, machine_name=None, machine_type="unknown", timeout=10, max_depth=5):
    """
    Main function to discover nodes and generate configuration.
    
    Args:
        server_url (str): OPC UA server URL
        machine_name (str): Optional machine name
        machine_type (str): Type of machine
        timeout (int): Connection timeout
        max_depth (int): Maximum recursion depth
    """
    discoverer = OPCUANodeDiscoverer(server_url, timeout, max_depth)
    
    try:
        # Discover nodes
        nodes = await discoverer.discover_nodes()
        
        if not nodes:
            log.error("No nodes discovered. Check server connection and configuration.")
            return
        
        # Print discovered nodes summary
        print(f"\n=== DISCOVERY SUMMARY ===")
        print(f"Server: {server_url}")
        print(f"Total sensor nodes discovered: {len(nodes)}")
        
        if nodes:
            # Show namespace distribution
            namespaces = {}
            for node_info in nodes.values():
                ns = node_info.get('namespace', 'unknown')
                namespaces[ns] = namespaces.get(ns, 0) + 1
            
            print(f"Namespace distribution: {dict(namespaces)}")
            
            print("\nDiscovered sensor nodes:")
            for i, (path, node_info) in enumerate(nodes.items()):
                if i >= 20:  # Show first 20 nodes
                    print(f"... and {len(nodes) - 20} more nodes")
                    break
                ns = node_info.get('namespace', '?')
                print(f"  [{ns}] {path}: {node_info['node_id']} = {node_info['value']}")
        else:
            print("No custom sensor nodes found. Make sure the OPC UA server has nodes in custom namespaces (ns > 0).")
        
        # Generate machine configuration
        config = discoverer.generate_machine_config(machine_name, machine_type)
        
        # Save configuration
        discoverer.save_config_to_file(config)
        
        print(f"\n=== CONFIGURATION GENERATED ===")
        print("Machine configuration has been added to the config file.")
        print("Review and modify the generated configuration as needed.")
        
    except KeyboardInterrupt:
        log.info("Discovery interrupted by user")
    except Exception as e:
        log.error(f"Error during discovery and configuration: {e}")

def main():
    # Create argument parser
    parser = argparse.ArgumentParser(
        description="Discover OPC UA nodes on a server and generate configuration files.",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
            Examples:
            python discover_nodes.py opc.tcp://localhost:4840
            python discover_nodes.py opc.tcp://localhost:4840 --machine-name "EOS_Printer_A" --machine-type "3d_printer"
            python discover_nodes.py opc.tcp://localhost:4840 --timeout 30 --max-depth 10
        """
    )

    # Required arguments
    parser.add_argument("url", type=str, help="OPC UA server URL (e.g., opc.tcp://localhost:4840)")
    
    # Optional arguments
    parser.add_argument("--machine-name", type=str, help="Name for the machine in config")
    parser.add_argument("--machine-type", type=str, default="unknown", 
                       help="Type of machine (e.g., '3d_printer', 'cnc_machine', 'power_meter')")
    parser.add_argument("--timeout", type=int, default=10, 
                       help="Connection timeout in seconds (default: 10)")
    parser.add_argument("--max-depth", type=int, default=5, 
                       help="Maximum recursion depth for node discovery (default: 5)")
    parser.add_argument("--verbose", "-v", action="store_true", 
                       help="Enable verbose logging")

    args = parser.parse_args()

    # Set logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Validate URL format
    if not args.url.startswith("opc.tcp://"):
        log.error("URL must start with 'opc.tcp://'")
        return 1
    
    print(f"=== OPC UA NODE DISCOVERER ===")
    print(f"Server URL: {args.url}")
    print(f"Machine Type: {args.machine_type}")
    print(f"Timeout: {args.timeout}s")
    print(f"Max Depth: {args.max_depth}")
    print("=" * 40)
    
    # Run the async discovery function
    try:
        asyncio.run(discover_and_configure(
            args.url, 
            args.machine_name, 
            args.machine_type, 
            args.timeout, 
            args.max_depth
        ))
    except Exception as e:
        log.error(f"Fatal error: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())