development:
  # Development environment for testing
  machines:
    # Test OPC UA machine
    EOS_Printer_Test:
      type: "3d_printer"
      opcua:
        url: "opc.tcp://localhost:4840"
        namespace: 4
        authentication:
          enabled: false
      nodes:
        temperature: "ns=4;s=EOS.Machine.Environment.Temperature"
        pressure: "ns=4;s=EOS.Machine.ProcessChamber.Pressure"
        status: "ns=4;s=EOS.Machine.Status.Current"
      monitoring:
        subscription_interval_ms: 1000
        connection_timeout_seconds: 10
        retry_attempts: 3
        retry_delay_seconds: 5
        
    # Test Modbus power meter
    # Test_Power_Meter:
    #   type: "power_meter"
    #   protocol: "modbus"
    #   modbus:
    #     host: "127.0.0.1"  # Localhost for testing
    #     port: 502
    #     timeout: 1
    #   registers:
    #     voltage_a_n: {"address": 0x0000, "count": 2, "type": "float32", "description": "Voltage A-N (V)"}
    #     current_a: {"address": 0x0006, "count": 2, "type": "float32", "description": "Current A (A)"}
    #     power_a: {"address": 0x000C, "count": 2, "type": "float32", "description": "Active Power A (W)"}
    #     total_system_power: {"address": 0x0034, "count": 2, "type": "float32", "description": "Total System Power (W)"}
    #   monitoring:
    #     poll_interval_seconds: 5
    #     connection_timeout_seconds: 10
    #     retry_attempts: 3
    #     retry_delay_seconds: 5
        
  influxdb:
    url: "http://localhost:8086"
    token: "your-development-token"
    org: "your-org"
    bucket: "test_manufacturing"
    batch_size: 50
    flush_interval_seconds: 5
    connection_timeout_seconds: 10

production:
  # Production environment configuration
  machines:
    # Real EOS 3D printer
    EOS_SI3654:
      type: "3d_printer"
      opcua:
        url: "opc.tcp://**************:4843"
        namespace: 4
        authentication:
          enabled: true
          username: "client1"
          password: "your-production-password"
      nodes:
        temperature: "ns=4;s=EOS.Machine.Environment.Temperature"
        pressure: "ns=4;s=EOS.Machine.ProcessChamber.Pressure"
        humidity: "ns=4;s=EOS.Machine.Environment.Humidity"
        status: "ns=4;s=EOS.Machine.Status.Current"
        build_progress: "ns=4;s=EOS.Machine.Process.BuildProgress"
      monitoring:
        subscription_interval_ms: 1000
        connection_timeout_seconds: 30
        retry_attempts: 10
        retry_delay_seconds: 15
        
    # Production power meters
    # EOS_SI3654_Power:
    #   type: "power_meter"
    #   protocol: "modbus"
    #   modbus:
    #     host: "***************"
    #     port: 8899
    #     timeout: 2
    #     auto_open: true
    #     auto_close: false
    #   registers:
    #     # Phase voltages (V) - starting at 0x0000, 6 registers (3 phases * 2 registers each)
    #     voltage_a_n: {"address": 0x0000, "count": 2, "type": "float32", "description": "Voltage A-N (V)"}
    #     voltage_b_n: {"address": 0x0002, "count": 2, "type": "float32", "description": "Voltage B-N (V)"}
    #     voltage_c_n: {"address": 0x0004, "count": 2, "type": "float32", "description": "Voltage C-N (V)"}
    #     # Phase currents (A) - starting at 0x0006, 6 registers
    #     current_a: {"address": 0x0006, "count": 2, "type": "float32", "description": "Current A (A)"}
    #     current_b: {"address": 0x0008, "count": 2, "type": "float32", "description": "Current B (A)"}
    #     current_c: {"address": 0x000A, "count": 2, "type": "float32", "description": "Current C (A)"}
    #     # Phase powers (W) - starting at 0x000C, 6 registers
    #     power_a: {"address": 0x000C, "count": 2, "type": "float32", "description": "Active Power A (W)"}
    #     power_b: {"address": 0x000E, "count": 2, "type": "float32", "description": "Active Power B (W)"}
    #     power_c: {"address": 0x0010, "count": 2, "type": "float32", "description": "Active Power C (W)"}
    #     # System totals
    #     total_system_power: {"address": 0x0034, "count": 2, "type": "float32", "description": "Total System Power (W)"}
    #     total_import_kwh: {"address": 0x0048, "count": 2, "type": "float32", "description": "Total Import (kWh)"}
    #     total_export_kwh: {"address": 0x004A, "count": 2, "type": "float32", "description": "Total Export (kWh)"}
    #     supply_frequency: {"address": 0x0046, "count": 2, "type": "float32", "description": "Supply Frequency (Hz)"}
    #     # Line-to-line voltages
    #     voltage_a_b: {"address": 0x00C8, "count": 2, "type": "float32", "description": "Voltage A-B (V)"}
    #     voltage_b_c: {"address": 0x00CA, "count": 2, "type": "float32", "description": "Voltage B-C (V)"}
    #     voltage_c_a: {"address": 0x00CC, "count": 2, "type": "float32", "description": "Voltage C-A (V)"}
    #   monitoring:
    #     poll_interval_seconds: 10
    #     connection_timeout_seconds: 15
    #     retry_attempts: 5
    #     retry_delay_seconds: 10

    # Sieve_Shaker_Power:
    #   type: "power_meter"
    #   protocol: "modbus"
    #   modbus:
    #     host: "***************"
    #     port: 8899
    #     timeout: 2
    #     auto_open: true
    #     auto_close: false
    #   registers:
    #     voltage_a_n: {"address": 0x0000, "count": 2, "type": "float32", "description": "Voltage A-N (V)"}
    #     voltage_b_n: {"address": 0x0002, "count": 2, "type": "float32", "description": "Voltage B-N (V)"}
    #     voltage_c_n: {"address": 0x0004, "count": 2, "type": "float32", "description": "Voltage C-N (V)"}
    #     current_a: {"address": 0x0006, "count": 2, "type": "float32", "description": "Current A (A)"}
    #     current_b: {"address": 0x0008, "count": 2, "type": "float32", "description": "Current B (A)"}
    #     current_c: {"address": 0x000A, "count": 2, "type": "float32", "description": "Current C (A)"}
    #     power_a: {"address": 0x000C, "count": 2, "type": "float32", "description": "Active Power A (W)"}
    #     power_b: {"address": 0x000E, "count": 2, "type": "float32", "description": "Active Power B (W)"}
    #     power_c: {"address": 0x0010, "count": 2, "type": "float32", "description": "Active Power C (W)"}
    #     total_system_power: {"address": 0x0034, "count": 2, "type": "float32", "description": "Total System Power (W)"}
    #     total_import_kwh: {"address": 0x0048, "count": 2, "type": "float32", "description": "Total Import (kWh)"}
    #     supply_frequency: {"address": 0x0046, "count": 2, "type": "float32", "description": "Supply Frequency (Hz)"}
    #   monitoring:
    #     poll_interval_seconds: 10
    #     connection_timeout_seconds: 15
    #     retry_attempts: 5
    #     retry_delay_seconds: 10

    # Air_Dryer_Power:
    #   type: "power_meter"
    #   protocol: "modbus"
    #   modbus:
    #     host: "***************"
    #     port: 8899
    #     timeout: 2
    #     auto_open: true
    #     auto_close: false
    #   registers:
    #     voltage_a_n: {"address": 0x0000, "count": 2, "type": "float32", "description": "Voltage A-N (V)"}
    #     voltage_b_n: {"address": 0x0002, "count": 2, "type": "float32", "description": "Voltage B-N (V)"}
    #     voltage_c_n: {"address": 0x0004, "count": 2, "type": "float32", "description": "Voltage C-N (V)"}
    #     current_a: {"address": 0x0006, "count": 2, "type": "float32", "description": "Current A (A)"}
    #     current_b: {"address": 0x0008, "count": 2, "type": "float32", "description": "Current B (A)"}
    #     current_c: {"address": 0x000A, "count": 2, "type": "float32", "description": "Current C (A)"}
    #     power_a: {"address": 0x000C, "count": 2, "type": "float32", "description": "Active Power A (W)"}
    #     power_b: {"address": 0x000E, "count": 2, "type": "float32", "description": "Active Power B (W)"}
    #     power_c: {"address": 0x0010, "count": 2, "type": "float32", "description": "Active Power C (W)"}
    #     total_system_power: {"address": 0x0034, "count": 2, "type": "float32", "description": "Total System Power (W)"}
    #     total_import_kwh: {"address": 0x0048, "count": 2, "type": "float32", "description": "Total Import (kWh)"}
    #     supply_frequency: {"address": 0x0046, "count": 2, "type": "float32", "description": "Supply Frequency (Hz)"}
    #   monitoring:
    #     poll_interval_seconds: 10
    #     connection_timeout_seconds: 15
    #     retry_attempts: 5
    #     retry_delay_seconds: 10

    # EOS_SI2373_Power:
    #   type: "power_meter"
    #   protocol: "modbus"
    #   modbus:
    #     host: "***************"
    #     port: 8899
    #     timeout: 2
    #     auto_open: true
    #     auto_close: false
    #   registers:
    #     voltage_a_n: {"address": 0x0000, "count": 2, "type": "float32", "description": "Voltage A-N (V)"}
    #     voltage_b_n: {"address": 0x0002, "count": 2, "type": "float32", "description": "Voltage B-N (V)"}
    #     voltage_c_n: {"address": 0x0004, "count": 2, "type": "float32", "description": "Voltage C-N (V)"}
    #     current_a: {"address": 0x0006, "count": 2, "type": "float32", "description": "Current A (A)"}
    #     current_b: {"address": 0x0008, "count": 2, "type": "float32", "description": "Current B (A)"}
    #     current_c: {"address": 0x000A, "count": 2, "type": "float32", "description": "Current C (A)"}
    #     power_a: {"address": 0x000C, "count": 2, "type": "float32", "description": "Active Power A (W)"}
    #     power_b: {"address": 0x000E, "count": 2, "type": "float32", "description": "Active Power B (W)"}
    #     power_c: {"address": 0x0010, "count": 2, "type": "float32", "description": "Active Power C (W)"}
    #     total_system_power: {"address": 0x0034, "count": 2, "type": "float32", "description": "Total System Power (W)"}
    #     total_import_kwh: {"address": 0x0048, "count": 2, "type": "float32", "description": "Total Import (kWh)"}
    #     supply_frequency: {"address": 0x0046, "count": 2, "type": "float32", "description": "Supply Frequency (Hz)"}
    #   monitoring:
    #     poll_interval_seconds: 10
    #     connection_timeout_seconds: 15
    #     retry_attempts: 5
    #     retry_delay_seconds: 10
        
  influxdb:
    url: "http://influxdb.ami.modelfactory.sg:8086"
    token: "BAUefNIXOaHhiGcJlHSOXUPeZFsECwKuw3Cz72EUr9fOtWZdLwYlHLUQn7Pzq__qqesPFjXYV2hyjCGItHx__Q==" # Token for "Generic RW token for test bucket"
    org: "266e2e2e067fbe5b" #AMI
    bucket: "testbucket"
    batch_size: 100
    flush_interval_seconds: 10
    connection_timeout_seconds: 15
